# Mysql数据库
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************************************************************************************
    username: root
    password: Q0SCATk7VRgyR4i
  redis:
    host: localhost
    port: 6379
    password:

# Oracle数据库
#spring:
#  datasource:
#    driver-class-name: oracle.jdbc.OracleDriver
#    url: ***********************************
#    username: SNOWY-PUB-ORACLE
#    password: 123456

# SQLServer配置
#spring:
#  datasource:
#    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
#    url: ************************************************************
#    username: sa
#    password: 123456

# PostgreSQL配置
#spring:
#  datasource:
#    driverClassName: org.postgresql.Driver
#    url: *****************************************************
#    username: postgres
#    password: 123456

# 达梦数据库
#spring:
#  datasource:
#    driver-class-name: dm.jdbc.driver.DmDriver
#    url: jdbc:dm://localhost:5236/snowy-pub-dm
#    username: SNOWY
#    password: 123456789
# #达梦数据库兼容问题，不需要在sql语句前加模式名的解决方法：
# #https://blog.csdn.net/myth8860/article/details/100557705

# 人大金仓数据库
#spring:
#  datasource:
#    driver-class-name: com.kingbase8.Driver
#    url: ***************************************************
#    username: SYSTEM
#    password: 123456
# #人大金仓数据库兼容问题，不需要加在sql语句中加public的解决方法：
# #在根目录data下的kingbase.conf文档里面找到search_path = '"$user",PUBLIC,sys_catalog'进行替换放开
# #重启数据库即可完全兼容，注意 sql中不能出现mysql中的关键字的单引号

# 认证中心配置 - 本地环境
auth:
  center:
    # 客户端ID（必填）- 从认证中心获取
    client-id: "your_local_client_id_here"
    # 客户端密钥（必填）- 从认证中心获取
    client-secret: "your_local_client_secret_here"
    # 认证中心授权地址（必填）
    authorize-url: "http://localhost:8084/auth/oauth2/authorize"
    # 获取token的地址（必填）
    token-url: "http://localhost:8084/auth/oauth2/token"
    # 撤销token的地址（必填）
    revoke-url: "http://localhost:8084/auth/oauth2/revoke"
    # 登出地址（必填）
    logout-url: "http://localhost:8084/auth/logout"
    # 回调地址（必填）- 必须与注册时的地址一致
    redirect-uri: "http://localhost:6010/api/sso/callback"
    # 授权范围（可选）- 默认为AUTH
    scope: "AUTH"
