# Mysql数据库
spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************************************************************
          username: lpzhjcrds
          password: Abc1234!
        dataCenter:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *****************************************************************************************************************************************************************************
          username: lpzhjcrds
          password: Abc1234!
        jyfx:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************************************************
          username: lpzhjcrds
          password: Abc1234!
        huiliu:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://*************:10005/huiliu?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=CONVERT_TO_NULL&useSSL=false&serverTimezone=UTC&nullCatalogMeansCurrent=true
          username: nkfzr2igic6pwfx
          password: 8jny6ctp0o1
  redis:
    host: localhost
    port: 6379
    database: 5
#验证码相关配置 去除日志打印
logging:
  level:
    com.anji: off
    com.concise: info
# Oracle数据库
#spring:
#  datasource:
#    driver-class-name: oracle.jdbc.OracleDriver
#    url: ***********************************
#    username: SNOWY-PUB-ORACLE
#    password: 123456

# SQLServer配置
#spring:
#  datasource:
#    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
#    url: ************************************************************
#    username: sa
#    password: 123456

# PostgreSQL配置
#spring:
#  datasource:
#    driverClassName: org.postgresql.Driver
#    url: *****************************************************
#    username: postgres
#    password: 123456

# 达梦数据库
#spring:
#  datasource:
#    driver-class-name: dm.jdbc.driver.DmDriver
#    url: jdbc:dm://localhost:5236/snowy-pub-dm
#    username: SNOWY
#    password: 123456789
# #达梦数据库兼容问题，不需要在sql语句前加模式名的解决方法：
# #https://blog.csdn.net/myth8860/article/details/100557705

# 人大金仓数据库
#spring:
#  datasource:
#    driver-class-name: com.kingbase8.Driver
#    url: ***************************************************
#    username: SYSTEM
#    password: 123456
# #人大金仓数据库兼容问题，不需要加在sql语句中加public的解决方法：
# #在根目录data下的kingbase.conf文档里面找到search_path = '"$user",PUBLIC,sys_catalog'进行替换放开
# #重启数据库即可完全兼容，注意 sql中不能出现mysql中的关键字的单引号
zzd:
  qrcode:
    appkey: xinyoulinxi_dingoa-HdI4jzQba63
    appsecret: T8CGkeESb610Y0cb9Rijqh6O6RGBut8Cv2d7gcSS
    remark: xinyoulinxi_dingoa
    domain: openplatform-pro.ding.zj.gov.cn
    tenantid: 196729

# 认证中心配置 - 生产环境
auth:
  center:
    # 客户端ID（必填）- 从认证中心获取
    client-id: "risen_IXVyVScZYjmTBq4x3kalog"
    # 客户端密钥（必填）- 从认证中心获取
    client-secret: "jc9soUvlh2e8rNHPoa1xNClunJyjeMjTo1UgsdknGRPJ8mqeSBKCGNadzNXDuHAi"
    # 认证中心授权地址（必填）
    authorize-url: "https://one.lpxxfw.cn:7200/auth/oauth2/authorize"
    # authorize-url: "https://one.lpxxfw.cn:7100/typt/oauth2/authorize"
    # 获取access_token的地址（必填）
    code-url: "https://one.lpxxfw.cn:7200/auth/oauth2/token"

    # 获取token的地址（必填）
    token-url: "https://10.32.153.29:7100/typt/public/user/getUserInfoByToken"
    # 撤销token的地址（必填）
    revoke-url: "https://one.lpxxfw.cn:7200/auth/oauth2/revoke"
    # 登出地址（必填）
    logout-url: "https://one.lpxxfw.cn:7200/auth/logout"
    # 回调地址（必填）- 必须与注册时的地址一致，使用实际域名
    redirect-uri: "http://10.32.153.42:8050/transfer"
    # 授权范围（可选）- 默认为AUTH
    scope: "AUTH"
